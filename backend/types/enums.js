// User related constants
const UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN',
  ORGANIZATION_ADMIN: 'ORGANI<PERSON>ATION_ADMIN',
  COMPLIANCE_OFFICER: 'COMPLIANCE_OFFICER',
  AUDITOR: 'AUDITOR',
  VERIFIER: 'VERIFIER',
  MARKETPLACE_ADMIN: 'MARKETPLACE_ADMIN',
  SUPPORT: 'SUPPORT',
  DEVELOPER: 'DEVELOPER',
  ANALYST: 'ANALYST',
  FINANCE: 'FINANCE',
  LEGAL: 'LEGAL',
  SALES: 'SALES',
  MARKETING: 'MARKETING',
  OPERATIONS: 'OPERATIONS',
  CUSTOM: 'CUSTOM'
};

// Organization related constants
const OrganizationStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  SUSPENDED: 'SUSPENDED',
  INACTIVE: 'INACTIVE'
};

const OrganizationSize = {
  STARTUP: 'STARTUP',
  SMALL: 'SMALL',
  MEDIUM: 'MEDIUM',
  LARGE: 'LARGE',
  ENTERPRISE: 'ENTERPRISE'
};

const VerificationStatus = {
  PENDING: 'PENDING',
  IN_REVIEW: 'IN_REVIEW',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED'
};

// Project related constants
const ProjectType = {
  RENEWABLE_ENERGY: 'RENEWABLE_ENERGY',
  FORESTRY: 'FORESTRY',
  AGRICULTURE: 'AGRICULTURE',
  WASTE_MANAGEMENT: 'WASTE_MANAGEMENT',
  ENERGY_EFFICIENCY: 'ENERGY_EFFICIENCY',
  TRANSPORTATION: 'TRANSPORTATION',
  INDUSTRIAL: 'INDUSTRIAL',
  BLUE_CARBON: 'BLUE_CARBON',
  DIRECT_AIR_CAPTURE: 'DIRECT_AIR_CAPTURE',
  BIOCHAR: 'BIOCHAR',
  OTHER: 'OTHER'
};

const ProjectStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  SUSPENDED: 'SUSPENDED',
  CANCELLED: 'CANCELLED'
};

// Carbon Credit related constants
const CarbonCreditStatus = {
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  LISTED: 'LISTED',
  SOLD: 'SOLD',
  RETIRED: 'RETIRED',
  TOKENIZED: 'TOKENIZED'
};

// Order related constants
const OrderType = {
  BUY: 'BUY',
  SELL: 'SELL'
};

const OrderStatus = {
  PENDING: 'PENDING',
  PARTIAL: 'PARTIAL',
  FILLED: 'FILLED',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED'
};

// Transaction related constants
const TransactionType = {
  PURCHASE: 'PURCHASE',
  SALE: 'SALE',
  TRANSFER: 'TRANSFER',
  RETIREMENT: 'RETIREMENT',
  TOKENIZATION: 'TOKENIZATION',
  FEE: 'FEE',
  REFUND: 'REFUND',
  DEPOSIT: 'DEPOSIT',
  WITHDRAWAL: 'WITHDRAWAL'
};

const TransactionStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

// Notification related constants
const NotificationType = {
  INFO: 'INFO',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  SUCCESS: 'SUCCESS'
};

const NotificationCategory = {
  SYSTEM: 'SYSTEM',
  TRANSACTION: 'TRANSACTION',
  ORDER: 'ORDER',
  VERIFICATION: 'VERIFICATION',
  COMPLIANCE: 'COMPLIANCE',
  MARKETPLACE: 'MARKETPLACE',
  WALLET: 'WALLET',
  ORGANIZATION: 'ORGANIZATION',
  PROJECT: 'PROJECT',
  CARBON_CREDIT: 'CARBON_CREDIT'
};

// Audit Log related constants
const AuditAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  VIEW: 'VIEW',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  APPROVE: 'APPROVE',
  REJECT: 'REJECT',
  TRANSFER: 'TRANSFER',
  RETIRE: 'RETIRE',
  TOKENIZE: 'TOKENIZE'
};

const AuditResource = {
  USER: 'USER',
  ORGANIZATION: 'ORGANIZATION',
  PROJECT: 'PROJECT',
  CARBON_CREDIT: 'CARBON_CREDIT',
  WALLET: 'WALLET',
  ORDER: 'ORDER',
  TRANSACTION: 'TRANSACTION',
  NOTIFICATION: 'NOTIFICATION',
  DOCUMENT: 'DOCUMENT',
  COMPLIANCE: 'COMPLIANCE'
};

module.exports = {
  UserRole,
  OrganizationStatus,
  OrganizationSize,
  VerificationStatus,
  ProjectType,
  ProjectStatus,
  CarbonCreditStatus,
  OrderType,
  OrderStatus,
  TransactionType,
  TransactionStatus,
  NotificationType,
  NotificationCategory,
  AuditAction,
  AuditResource
};
