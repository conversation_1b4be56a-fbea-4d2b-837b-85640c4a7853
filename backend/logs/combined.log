{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T08:54:30.463Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:05:59.321Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:08:02.312Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:09:12.087Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:09:50.324Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:10:10.237Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:11:50.649Z"}
{"level":"error","message":"Unable to start server:","name":"SequelizeConnectionRefusedError","original":{"code":"ECONNREFUSED"},"parent":{"code":"ECONNREFUSED"},"service":"carbonx-backend","stack":"SequelizeConnectionRefusedError\n    at Client._connectionCallback (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/postgres/connection-manager.js:133:24)\n    at Client._handleErrorWhileConnecting (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:336:19)\n    at Client._handleErrorEvent (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/client.js:346:19)\n    at Connection.emit (node:events:518:28)\n    at Socket.reportStreamError (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/pg/lib/connection.js:57:12)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-06-26T11:11:53.114Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"carbonx-backend","timestamp":"2025-06-26T11:14:36.497Z"}
{"level":"info","message":"Database models synchronized.","service":"carbonx-backend","timestamp":"2025-06-26T11:14:41.637Z"}
{"level":"info","message":"Server is running on port 3001","service":"carbonx-backend","timestamp":"2025-06-26T11:14:41.642Z"}
{"level":"info","message":"Environment: development","service":"carbonx-backend","timestamp":"2025-06-26T11:14:41.642Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"carbonx-backend","timestamp":"2025-06-26T11:14:50.894Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"carbonx-backend","timestamp":"2025-06-26T11:14:50.938Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"carbonx-backend","timestamp":"2025-06-26T11:14:50.965Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"carbonx-backend","timestamp":"2025-06-26T11:15:01.336Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"carbonx-backend","timestamp":"2025-06-26T11:15:04.065Z"}
{"level":"error","message":"Unable to start server: ConnectionManager.getConnection was called after the connection manager was closed!","service":"carbonx-backend","stack":"Error: ConnectionManager.getConnection was called after the connection manager was closed!\n    at ConnectionManager.getConnection (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/connection-manager.js:70:13)\n    at /Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/sequelize.js:305:111\n    at /Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/retry-as-promised/dist/index.js:65:25\n    at new Promise (<anonymous>)\n    at retryAsPromised (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/retry-as-promised/dist/index.js:54:12)\n    at Sequelize.query (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/sequelize.js:300:12)\n    at PostgresQueryInterface.changeColumn (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:213:27)\n    at Organization.sync (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/model.js:984:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Sequelize.sync (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/sequelize.js:377:9)","timestamp":"2025-06-26T11:15:04.093Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"carbonx-backend","timestamp":"2025-06-26T11:15:07.312Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"carbonx-backend","timestamp":"2025-06-26T11:15:17.477Z"}
{"level":"error","message":"Unable to start server: ConnectionManager.getConnection was called after the connection manager was closed!","service":"carbonx-backend","stack":"Error: ConnectionManager.getConnection was called after the connection manager was closed!\n    at ConnectionManager.getConnection (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/connection-manager.js:70:13)\n    at /Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/sequelize.js:305:111\n    at /Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/retry-as-promised/dist/index.js:65:25\n    at new Promise (<anonymous>)\n    at retryAsPromised (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/retry-as-promised/dist/index.js:54:12)\n    at Sequelize.query (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/sequelize.js:300:12)\n    at PostgresQueryInterface.changeColumn (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:213:27)\n    at Order.sync (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/model.js:984:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Sequelize.sync (/Users/<USER>/Documents/carbonix/carbonx/backend/node_modules/sequelize/lib/sequelize.js:377:9)","timestamp":"2025-06-26T11:15:17.495Z"}
