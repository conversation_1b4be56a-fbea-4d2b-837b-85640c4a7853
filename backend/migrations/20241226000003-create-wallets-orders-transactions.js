'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Wallets table
    await queryInterface.createTable('wallets', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      address: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      network: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      chain_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      balance: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: '0',
      },
      native_balance: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: '0',
      },
      last_sync_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      organization_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'organizations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add unique constraint for address + network
    await queryInterface.addIndex('wallets', ['address', 'network'], {
      unique: true,
      name: 'wallets_address_network_unique'
    });

    // Create Orders table
    await queryInterface.createTable('orders', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      type: {
        type: Sequelize.ENUM('BUY', 'SELL'),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      price: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'PARTIAL', 'FILLED', 'CANCELLED', 'EXPIRED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      buyer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      seller_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      carbon_credit_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'carbon_credits',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Create Transactions table
    await queryInterface.createTable('transactions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      amount: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      fee: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      gas_price: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      gas_limit: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      gas_used: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      max_fee_per_gas: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      max_priority_fee_per_gas: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      type: {
        type: Sequelize.ENUM(
          'PURCHASE', 'SALE', 'TRANSFER', 'RETIREMENT', 'TOKENIZATION',
          'FEE', 'REFUND', 'DEPOSIT', 'WITHDRAWAL'
        ),
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED'),
        allowNull: false,
        defaultValue: 'PENDING',
      },
      transaction_hash: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
      },
      block_number: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      network: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      wallet_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'wallets',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      order_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'orders',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('transactions');
    await queryInterface.dropTable('orders');
    await queryInterface.dropTable('wallets');
  }
};
