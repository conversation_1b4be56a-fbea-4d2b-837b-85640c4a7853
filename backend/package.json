{"name": "carbonx-backend", "version": "1.0.0", "description": "Carbon Exchange Backend API", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:seed:undo": "sequelize-cli db:seed:undo:all", "db:create": "sequelize-cli db:create", "db:drop": "sequelize-cli db:drop", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "sequelize": "^6.35.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.2", "otplib": "^12.0.1", "qrcode": "^1.5.4", "ethers": "^6.13.7", "alchemy-sdk": "^3.5.8", "winston": "^3.11.0", "zod": "^3.24.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=18.0.0"}}