const { verifyAccessToken } = require('../utils/jwt');
const { CustomError } = require('./errorHandler');
const { UserRole } = require('../types/enums');

const authenticate = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new CustomError('Access token required', 401);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    const payload = verifyAccessToken(token);

    req.user = payload;
    next();
  } catch (error) {
    next(new CustomError('Invalid or expired token', 401));
  }
};

const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    if (roles.length > 0 && !roles.includes(req.user.role)) {
      return next(new CustomError('Insufficient permissions', 403));
    }

    next();
  };
};

const requireOrganization = (req, res, next) => {
  if (!req.user) {
    return next(new CustomError('Authentication required', 401));
  }

  if (!req.user.organizationId) {
    return next(new CustomError('Organization membership required', 403));
  }

  next();
};

const requireOrganizationAccess = (req, res, next) => {
  if (!req.user) {
    return next(new CustomError('Authentication required', 401));
  }

  const organizationId = req.params.organizationId || req.body.organizationId || req.query.organizationId;

  if (!organizationId) {
    return next(new CustomError('Organization ID required', 400));
  }

  // Allow access if user belongs to the organization or is an admin
  const hasAccess =
    req.user.organizationId === organizationId ||
    req.user.role === UserRole.ADMIN ||
    req.user.role === UserRole.SUPER_ADMIN;

  if (!hasAccess) {
    return next(new CustomError('Access denied to this organization', 403));
  }

  next();
};

const optionalAuth = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const payload = verifyAccessToken(token);
      req.user = payload;
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

module.exports = {
  authenticate,
  authorize,
  requireOrganization,
  requireOrganizationAccess,
  optionalAuth
};
