import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { Op } from 'sequelize';
import { CarbonCredit } from '@/models/CarbonCredit';
import { Project } from '@/models/Project';
import { User } from '@/models/User';
import { Organization } from '@/models/Organization';
import { asyncHandler, CustomError } from '@/middleware/errorHandler';
import { authenticate, requireOrganization } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import { CarbonCreditStatus, VerificationStatus } from '@/types/enums';

const router = Router();

// All carbon credit routes require authentication
router.use(authenticate);

// Validation schemas
const carbonCreditValidation = [
  body('name').isLength({ min: 2 }).trim(),
  body('description').optional().isLength({ max: 1000 }).trim(),
  body('quantity').isFloat({ min: 0.01 }),
  body('price').isFloat({ min: 0.01 }),
  body('vintage').isInt({ min: 1990, max: new Date().getFullYear() + 10 }),
  body('standard').isLength({ min: 2 }).trim(),
  body('methodology').isLength({ min: 2 }).trim(),
  body('projectId').isUUID(),
  body('minPurchaseQuantity').optional().isFloat({ min: 0.01 }),
  body('location').optional().isLength({ min: 2 }).trim(),
  body('country').optional().isLength({ min: 2 }).trim(),
  body('externalProjectId').optional().isLength({ min: 1 }).trim(),
  body('serialNumber').optional().isLength({ min: 1 }).trim(),
  body('verificationBody').optional().isLength({ min: 2 }).trim(),
];

/**
 * GET /api/carbon-credits
 * Get carbon credits with filtering and pagination
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const {
    page = 1,
    limit = 10,
    status,
    vintage,
    standard,
    methodology,
    minPrice,
    maxPrice,
    search,
    organizationId
  } = req.query;

  const offset = (Number(page) - 1) * Number(limit);
  const whereClause: any = {};

  // Filter by status
  if (status) {
    whereClause.status = status;
  }

  // Filter by vintage
  if (vintage) {
    whereClause.vintage = vintage;
  }

  // Filter by standard
  if (standard) {
    whereClause.standard = standard;
  }

  // Filter by methodology
  if (methodology) {
    whereClause.methodology = methodology;
  }

  // Filter by price range
  if (minPrice || maxPrice) {
    whereClause.price = {};
    if (minPrice) whereClause.price[Op.gte] = Number(minPrice);
    if (maxPrice) whereClause.price[Op.lte] = Number(maxPrice);
  }

  // Search by name or description
  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Filter by organization (if specified)
  if (organizationId) {
    whereClause.organizationId = organizationId;
  }

  const { rows: carbonCredits, count } = await CarbonCredit.findAndCountAll({
    where: whereClause,
    limit: Number(limit),
    offset,
    order: [['createdAt', 'DESC']],
    include: [
      { model: Project, as: 'project' },
      { model: User, as: 'user', attributes: ['id', 'name', 'email'] },
      { model: Organization, as: 'organization', attributes: ['id', 'name'] }
    ]
  });

  res.json({
    carbonCredits,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total: count,
      pages: Math.ceil(count / Number(limit))
    }
  });
}));

/**
 * GET /api/carbon-credits/organization
 * Get carbon credits for user's organization
 */
router.get('/organization', requireOrganization, asyncHandler(async (req: Request, res: Response) => {
  const carbonCredits = await CarbonCredit.findAll({
    where: {
      organizationId: req.user!.organizationId
    },
    order: [['createdAt', 'DESC']],
    include: [
      { model: Project, as: 'project' },
      { model: User, as: 'user', attributes: ['id', 'name', 'email'] }
    ]
  });

  res.json({ carbonCredits });
}));

/**
 * POST /api/carbon-credits
 * Create a new carbon credit
 */
router.post('/', requireOrganization, carbonCreditValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const {
    name, description, quantity, price, vintage, standard, methodology,
    projectId, minPurchaseQuantity, location, country, externalProjectId,
    serialNumber, verificationBody, images, metadata
  } = req.body;

  // Verify project belongs to user's organization
  const project = await Project.findOne({
    where: {
      id: projectId,
      organizationId: req.user!.organizationId
    }
  });

  if (!project) {
    throw new CustomError('Project not found or access denied', 404);
  }

  // Create carbon credit
  const carbonCredit = await CarbonCredit.create({
    name,
    description,
    quantity,
    availableQuantity: quantity,
    retiredQuantity: 0,
    price,
    minPurchaseQuantity,
    vintage,
    standard,
    methodology,
    location,
    country,
    externalProjectId,
    serialNumber,
    verificationBody,
    status: CarbonCreditStatus.PENDING,
    verificationStatus: VerificationStatus.PENDING,
    images: images || [],
    metadata,
    userId: req.user!.id,
    organizationId: req.user!.organizationId!,
    projectId,
  });

  logger.info(`Carbon credit ${carbonCredit.id} created by user ${req.user!.id}`);

  res.status(201).json({
    carbonCredit,
    message: 'Carbon credit created successfully'
  });
}));

export default router;