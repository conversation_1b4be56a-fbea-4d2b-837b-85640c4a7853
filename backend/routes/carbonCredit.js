import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import { Op } from 'sequelize';
import { CarbonCredit } from '@/models/CarbonCredit';
import { Project } from '@/models/Project';
import { User } from '@/models/User';
import { Organization } from '@/models/Organization';
import { asyncHandler, CustomError } from '@/middleware/errorHandler';
import { authenticate, requireOrganization } from '@/middleware/auth';
import { logger } from '@/utils/logger';
import { CarbonCreditStatus, VerificationStatus } from '@/types/enums';
import { sequelize } from '@/config/database';

const router = Router();

// All carbon credit routes require authentication
router.use(authenticate);

// Validation schemas
const carbonCreditValidation = [
  body('name').isLength({ min: 2 }).trim(),
  body('description').optional().isLength({ max: 1000 }).trim(),
  body('quantity').isFloat({ min: 0.01 }),
  body('price').isFloat({ min: 0.01 }),
  body('vintage').isInt({ min: 1990, max: new Date().getFullYear() + 10 }),
  body('standard').isLength({ min: 2 }).trim(),
  body('methodology').isLength({ min: 2 }).trim(),
  body('projectId').isUUID(),
  body('minPurchaseQuantity').optional().isFloat({ min: 0.01 }),
  body('location').optional().isLength({ min: 2 }).trim(),
  body('country').optional().isLength({ min: 2 }).trim(),
  body('externalProjectId').optional().isLength({ min: 1 }).trim(),
  body('serialNumber').optional().isLength({ min: 1 }).trim(),
  body('verificationBody').optional().isLength({ min: 2 }).trim(),
];

/**
 * GET /api/carbon-credits
 * Get carbon credits with filtering and pagination
 */
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    status,
    vintage,
    standard,
    methodology,
    minPrice,
    maxPrice,
    search,
    organizationId
  } = req.query;

  const offset = (Number(page) - 1) * Number(limit);
  const whereClause = {};

  // Filter by status
  if (status) {
    whereClause.status = status;
  }

  // Filter by vintage
  if (vintage) {
    whereClause.vintage = vintage;
  }

  // Filter by standard
  if (standard) {
    whereClause.standard = standard;
  }

  // Filter by methodology
  if (methodology) {
    whereClause.methodology = methodology;
  }

  // Filter by price range
  if (minPrice || maxPrice) {
    whereClause.price = {};
    if (minPrice) whereClause.price[Op.gte] = Number(minPrice);
    if (maxPrice) whereClause.price[Op.lte] = Number(maxPrice);
  }

  // Search by name or description
  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Filter by organization (if specified)
  if (organizationId) {
    whereClause.organizationId = organizationId;
  }

  const { rows: carbonCredits, count } = await CarbonCredit.findAndCountAll({
    where: whereClause,
    limit: Number(limit),
    offset,
    order: [['createdAt', 'DESC']],
    include: [
      { model: Project, as: 'project' },
      { model: User, as: 'user', attributes: ['id', 'name', 'email'] },
      { model: Organization, as: 'organization', attributes: ['id', 'name'] }
    ]
  });

  res.json({
    carbonCredits,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total: count,
      pages: Math.ceil(count / Number(limit))
    }
  });
}));

/**
 * GET /api/carbon-credits/organization
 * Get carbon credits for user's organization
 */
router.get('/organization', requireOrganization, asyncHandler(async (req, res) => {
  const carbonCredits = await CarbonCredit.findAll({
    where: {
      organizationId: req.user.organizationId
    },
    order: [['createdAt', 'DESC']],
    include: [
      { model: Project, as: 'project' },
      { model: User, as: 'user', attributes: ['id', 'name', 'email'] }
    ]
  });

  res.json({ carbonCredits });
}));

/**
 * POST /api/carbon-credits
 * Create a new carbon credit
 */
router.post('/', requireOrganization, carbonCreditValidation, asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const {
    name, description, quantity, price, vintage, standard, methodology,
    projectId, minPurchaseQuantity, location, country, externalProjectId,
    serialNumber, verificationBody, images, metadata
  } = req.body;

  // Start database transaction
  const transaction = await sequelize.transaction();

  try {
    // Verify project belongs to user's organization
    const project = await Project.findOne({
      where: {
        id: projectId,
        organizationId: req.user.organizationId
      },
      transaction
    });

    if (!project) {
      await transaction.rollback();
      throw new CustomError('Project not found or access denied', 404);
    }

    // Create carbon credit
    const carbonCredit = await CarbonCredit.create({
      name,
      description,
      quantity,
      availableQuantity: quantity,
      retiredQuantity: 0,
      price,
      minPurchaseQuantity,
      vintage,
      standard,
      methodology,
      location,
      country,
      externalProjectId,
      serialNumber,
      verificationBody,
      status: CarbonCreditStatus.PENDING,
      verificationStatus: VerificationStatus.PENDING,
      images: images || [],
      metadata,
      userId: req.user.id,
      organizationId: req.user.organizationId,
      projectId,
    }, { transaction });

    // Commit the transaction
    await transaction.commit();

    logger.info(`Carbon credit ${carbonCredit.id} created by user ${req.user.id}`);

    res.status(201).json({
      carbonCredit,
      message: 'Carbon credit created successfully'
    });

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    logger.error(`Error creating carbon credit: ${error.message}`, error);
    throw error;
  }
}));

/**
 * PUT /api/carbon-credits/:id
 * Update a carbon credit
 */
router.put('/:id', requireOrganization, carbonCreditValidation, asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const {
    name, description, quantity, price, vintage, standard, methodology,
    projectId, minPurchaseQuantity, location, country, externalProjectId,
    serialNumber, verificationBody, images, metadata
  } = req.body;

  // Start database transaction
  const transaction = await sequelize.transaction();

  try {
    // Find the carbon credit and verify ownership
    const carbonCredit = await CarbonCredit.findOne({
      where: {
        id,
        organizationId: req.user.organizationId
      },
      transaction
    });

    if (!carbonCredit) {
      await transaction.rollback();
      throw new CustomError('Carbon credit not found or access denied', 404);
    }

    // If projectId is being changed, verify the new project belongs to user's organization
    if (projectId && projectId !== carbonCredit.projectId) {
      const project = await Project.findOne({
        where: {
          id: projectId,
          organizationId: req.user.organizationId
        },
        transaction
      });

      if (!project) {
        await transaction.rollback();
        throw new CustomError('Project not found or access denied', 404);
      }
    }

    // Update carbon credit
    const updatedCarbonCredit = await carbonCredit.update({
      name,
      description,
      quantity,
      availableQuantity: quantity, // Reset available quantity to new total
      price,
      minPurchaseQuantity,
      vintage,
      standard,
      methodology,
      location,
      country,
      externalProjectId,
      serialNumber,
      verificationBody,
      images: images || carbonCredit.images,
      metadata,
      projectId: projectId || carbonCredit.projectId,
    }, { transaction });

    // Commit the transaction
    await transaction.commit();

    logger.info(`Carbon credit ${id} updated by user ${req.user.id}`);

    res.json({
      carbonCredit: updatedCarbonCredit,
      message: 'Carbon credit updated successfully'
    });

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    logger.error(`Error updating carbon credit ${id}: ${error.message}`, error);
    throw error;
  }
}));

/**
 * DELETE /api/carbon-credits/:id
 * Delete a carbon credit
 */
router.delete('/:id', requireOrganization, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Start database transaction
  const transaction = await sequelize.transaction();

  try {
    // Find the carbon credit and verify ownership
    const carbonCredit = await CarbonCredit.findOne({
      where: {
        id,
        organizationId: req.user.organizationId
      },
      transaction
    });

    if (!carbonCredit) {
      await transaction.rollback();
      throw new CustomError('Carbon credit not found or access denied', 404);
    }

    // Check if carbon credit can be deleted (e.g., not sold or retired)
    if (carbonCredit.status === CarbonCreditStatus.SOLD || carbonCredit.status === CarbonCreditStatus.RETIRED) {
      await transaction.rollback();
      throw new CustomError('Cannot delete carbon credit that has been sold or retired', 400);
    }

    // Delete the carbon credit
    await carbonCredit.destroy({ transaction });

    // Commit the transaction
    await transaction.commit();

    logger.info(`Carbon credit ${id} deleted by user ${req.user.id}`);

    res.json({
      message: 'Carbon credit deleted successfully'
    });

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    logger.error(`Error deleting carbon credit ${id}: ${error.message}`, error);
    throw error;
  }
}));

/**
 * PATCH /api/carbon-credits/batch
 * Batch update carbon credits
 */
router.patch('/batch', requireOrganization, asyncHandler(async (req, res) => {
  const { carbonCreditIds, updateData } = req.body;

  if (!carbonCreditIds || !Array.isArray(carbonCreditIds) || carbonCreditIds.length === 0) {
    throw new CustomError('Carbon credit IDs array is required', 400);
  }

  if (!updateData || typeof updateData !== 'object') {
    throw new CustomError('Update data is required', 400);
  }

  // Start database transaction
  const transaction = await sequelize.transaction();

  try {
    // Find all carbon credits and verify ownership
    const carbonCredits = await CarbonCredit.findAll({
      where: {
        id: {
          [Op.in]: carbonCreditIds
        },
        organizationId: req.user.organizationId
      },
      transaction
    });

    if (carbonCredits.length !== carbonCreditIds.length) {
      await transaction.rollback();
      throw new CustomError('Some carbon credits not found or access denied', 404);
    }

    // Prepare update data (only allow certain fields to be updated in batch)
    const allowedFields = ['price', 'minPurchaseQuantity', 'description', 'status'];
    const filteredUpdateData = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredUpdateData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredUpdateData).length === 0) {
      await transaction.rollback();
      throw new CustomError('No valid fields to update', 400);
    }

    // Update all carbon credits
    const [updatedCount] = await CarbonCredit.update(
      filteredUpdateData,
      {
        where: {
          id: {
            [Op.in]: carbonCreditIds
          },
          organizationId: req.user.organizationId
        },
        transaction
      }
    );

    // Commit the transaction
    await transaction.commit();

    logger.info(`Batch updated ${updatedCount} carbon credits by user ${req.user.id}`);

    res.json({
      message: `Successfully updated ${updatedCount} carbon credits`,
      updatedCount,
      updatedFields: Object.keys(filteredUpdateData)
    });

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    logger.error(`Error in batch update: ${error.message}`, error);
    throw error;
  }
}));

/**
 * POST /api/carbon-credits/:id/retire
 * Retire carbon credits
 */
router.post('/:id/retire', requireOrganization, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { quantity, reason, beneficiary } = req.body;

  if (!quantity || quantity <= 0) {
    throw new CustomError('Valid quantity is required', 400);
  }

  // Start database transaction
  const transaction = await sequelize.transaction();

  try {
    // Find the carbon credit and verify ownership
    const carbonCredit = await CarbonCredit.findOne({
      where: {
        id,
        organizationId: req.user.organizationId
      },
      transaction
    });

    if (!carbonCredit) {
      await transaction.rollback();
      throw new CustomError('Carbon credit not found or access denied', 404);
    }

    // Check if enough quantity is available
    if (carbonCredit.availableQuantity < quantity) {
      await transaction.rollback();
      throw new CustomError('Insufficient available quantity', 400);
    }

    // Update carbon credit quantities
    const updatedCarbonCredit = await carbonCredit.update({
      availableQuantity: carbonCredit.availableQuantity - quantity,
      retiredQuantity: carbonCredit.retiredQuantity + quantity,
      status: carbonCredit.availableQuantity - quantity === 0 ? CarbonCreditStatus.RETIRED : carbonCredit.status,
      retirementDate: new Date(),
      retirementReason: reason,
      retirementBeneficiary: beneficiary
    }, { transaction });

    // Commit the transaction
    await transaction.commit();

    logger.info(`Retired ${quantity} units of carbon credit ${id} by user ${req.user.id}`);

    res.json({
      carbonCredit: updatedCarbonCredit,
      message: `Successfully retired ${quantity} units of carbon credit`,
      retiredQuantity: quantity
    });

  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    logger.error(`Error retiring carbon credit ${id}: ${error.message}`, error);
    throw error;
  }
}));

export default router;