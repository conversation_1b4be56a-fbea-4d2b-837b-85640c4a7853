const { Router } = require('express');
const { sequelize } = require('../config/database');
const { logger } = require('../utils/logger');
const { asyncHandler } = require('../middleware/errorHandler');

const router = Router();

/**
 * Health check endpoint
 * GET /api/health
 */
router.get('/', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  const checks = {
    database: { status: "unknown" },
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.APP_VERSION || '1.0.0',
  };

  // Check database connection
  try {
    await sequelize.authenticate();
    checks.database = {
      status: "healthy",
      responseTime: `${Date.now() - startTime}ms`,
    };
  } catch (error) {
    logger.error("Database health check failed:", error);
    checks.database = {
      status: "unhealthy",
      error: "Database connection failed",
      responseTime: `${Date.now() - startTime}ms`,
    };
  }

  // Determine overall status
  const isHealthy = checks.database.status === "healthy";

  // Log health check result
  logger.info("Health check completed", {
    healthy: isHealthy,
    responseTime: `${Date.now() - startTime}ms`,
  });

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? "healthy" : "unhealthy",
    checks,
    responseTime: `${Date.now() - startTime}ms`,
  });
}));

module.exports = router;
